<?php

namespace App\Http\Controllers\Web;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
    public function index()
    {
        return view("auth.login");
    }

    public function login(Request $request)
    {
        try {
            $credentials = $request->validate([
                'email' => ['required', 'email', 'exists:users,email'],
                'password' => [
                    'required',
                    Password::min(8)->mixedCase()->letters()->numbers()->symbols()->max(20),
                ],
            ]);

            logger()->info('Login attempt', ['email' => $request->email]);

            if (Auth::attempt($credentials)) {
                $request->session()->regenerate();

                $user = Auth::user();

                Log::info('User login successful', [
                    // Auth Info (if already authenticated)
                    'user_id'        => Auth::id(),
                    'user_role'      => $user->role,
                    'email'          => $request->email ?? null,

                    // Request Info
                    'ip_address'     => $request->ip(),
                    'user_agent'     => $request->userAgent(),
                    'session_id'     => session()->getId(),
                    'request_url'    => $request->fullUrl(),
                    'request_method' => $request->method(),

                    // Server Info
                    'host'           => $request->getHost(),
                    'server_port'    => $request->getPort(),
                    'referer'        => $request->headers->get('referer'),

                    // Device/Browser Info (basic parsing)
                    // 'platform'       => get_platform($request->userAgent()),
                    // 'browser'        => get_browser_name($request->userAgent()),

                    // Geo info if using IP geolocation services (optional)
                    // 'country'     => geoip()->getLocation($request->ip())->country ?? null,

                    // Timestamp
                    'login_time'     => now()->toDateTimeString(),
                ]);

                // Redirect based on user role
                if ($user->role === '1') {
                    // Admin user - redirect to dashboard
                    return redirect()->route('dashboard')->with('success', 'Welcome back, Admin!');
                } else {
                    // Regular user - redirect to home page
                    return redirect()->route('home')->with('success', 'Login successful!');
                }
            }

            logger()->warning('Login failed', ['email' => $request->email]);

            return back()->withErrors([
                'email' => 'The provided credentials do not match our records.',
            ])->onlyInput('email');
        } catch (Exception $e) {
            Log::error('Login error occurred', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'time' => now()->toDateTimeString(),
            ]);

            return back()->withErrors([
                'email' => 'The provided email address is not registered.',
            ])->onlyInput('email');
        }
    }

    public function logout(Request $request)
    {
        $user = Auth::user();

        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        Session::forget('some_session_key');

        Log::info('User logged out', [
            'user_uuid' => optional($user)->users_id ?? optional($user)->id,
            'email'     => optional($user)->email,
            'ip'        => $request->ip(),
            'user_agent' => $request->userAgent(),
            'logged_out_at' => now()->toDateTimeString(),
        ]);

        return redirect()->route('login')->with('success', 'You have been logged out successfully.');
    }

    public function register()
    {
        return view("auth.register");
    }

    public function registerPost(Request $request)
    {
        // Validate the registration data
        $validatedData = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => [
                'required',
                'confirmed',
                Password::min(8)->mixedCase()->letters()->numbers()->symbols()->max(20),
            ],
            'mobile' => ['nullable', 'string', 'max:20'],
            'terms' => ['required', 'accepted'],
        ], [
            'name.required' => 'Full name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required.',
            'password.confirmed' => 'Password confirmation does not match.',
            'terms.required' => 'You must agree to the Terms & Conditions.',
            'terms.accepted' => 'You must agree to the Terms & Conditions.',
        ]);

        try {

            // Create the user
            $user = User::create([
                'name' => $validatedData['name'],
                'email' => $validatedData['email'],
                'password' => Hash::make($validatedData['password']),
                'mobile' => $validatedData['mobile'] ?? null,
                'role' => '0', // Default role for regular users
            ]);

            // Log the registration
            Log::info('New user registered', [
                'user_id' => $user->users_id,
                'email' => $user->email,
                'name' => $user->name,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'registered_at' => now()->toDateTimeString(),
            ]);

            // Automatically log in the user
            Auth::login($user);
            $request->session()->regenerate();

            // Log the automatic login
            Log::info('User automatically logged in after registration', [
                'user_id' => $user->users_id,
                'email' => $user->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'login_time' => now()->toDateTimeString(),
            ]);

            return redirect()->route('home')->with('success', 'Registration successful! Welcome to our platform.');

        } catch (Exception $e) {
            Log::error('Registration error occurred', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'email' => $request->email ?? 'N/A',
                'time' => now()->toDateTimeString(),
            ]);

            return back()->withErrors([
                'email' => 'Registration failed. Please try again.',
            ])->withInput($request->except('password', 'password_confirmation'));
        }
    }
}
